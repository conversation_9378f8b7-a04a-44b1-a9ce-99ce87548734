---
description: 
globs: 
alwaysApply: false
---
# JeecgBoot-Vue3 开发规范

## 项目介绍
本项目是基于 Vue 3 + TypeScript + Ant Design Vue 的前端项目，使用 Vite 作为构建工具。

## 技术栈规范
- Vue 3.5.13+
- TypeScript 4.9.5+
- Ant Design Vue 4.2.6+
- Vite 6.0.7+
- Node.js 18+ 或 20+

## 目录结构说明
```
src/
├── api/                # API 接口定义
├── assets/            # 静态资源文件
├── components/        # 公共组件
├── layouts/           # 布局组件
├── router/            # 路由配置
├── store/             # 状态管理
├── utils/             # 工具函数
└── views/             # 页面组件
```

## 开发规范

### 1. 命名规范
- 文件命名：
  - 组件文件使用 PascalCase（首字母大写）：`UserProfile.vue`
  - 其他文件使用 kebab-case（短横线）：`user-api.ts`
- 组件命名：
  - 组件名使用 PascalCase
  - 基础组件以特定前缀开头（如 Base、Common）
- 变量命名：
  - 普通变量使用 camelCase
  - 常量使用 UPPER_SNAKE_CASE
  - 接口名以 I 开头，类型以 T 开头

### 2. 代码风格
- 使用 ESLint + Prettier 进行代码格式化
- 使用 TypeScript 类型标注
- 组件使用 `<script setup>` 语法
- Props 必须声明类型
- 避免使用 any 类型

### 3. 组件开发规范
- 组件文件结构：
  ```vue
  <template>
    <!-- 模板代码 -->
  </template>

  <script setup lang="ts">
    // 导入语句
    // 类型定义
    // 组件逻辑
  </script>

  <style lang="less" scoped>
    // 样式代码
  </style>
  ```
- 组件属性顺序：
  1. v-model
  2. ref, v-for key
  3. v-if/v-show
  4. v-on
  5. v-bind
  6. 其他属性
  7. class
  8. style

### 4. API 接口规范
- API 接口按模块划分，统一放在 api 目录下
- 使用 TypeScript 接口定义请求和响应类型
- 接口命名规范：`useXxxApi`

### 5. 状态管理规范
- 使用 Pinia 进行状态管理
- Store 按功能模块拆分
- 异步操作使用 actions
- 避免在组件中直接修改 state

### 6. 路由规范
- 路由配置集中管理在 router 目录
- 路由命名使用 kebab-case
- 路由组件使用异步加载
- 必要的路由需要添加权限控制

### 7. Git 提交规范
- 使用 commitlint 规范提交信息
- 提交格式：`type(scope): subject`
- type 类型：
  - feat: 新功能
  - fix: 修复
  - docs: 文档
  - style: 格式
  - refactor: 重构
  - test: 测试
  - chore: 构建

### 8. 样式规范
- 使用 Less 预处理器
- 组件样式使用 scoped
- 全局样式放在 assets/less 目录
- 使用 BEM 命名规范
- 优先使用 Ant Design Vue 的样式变量

### 9. 性能优化规范
- 合理使用异步组件
- 图片资源适当压缩
- 及时清理无用代码和依赖
- 避免重复请求相同资源
- 合理使用缓存

### 10. 安全规范
- 敏感信息不得硬编码
- 用户输入需要做好验证和转义
- API 请求做好错误处理
- 注意防范 XSS 和 CSRF 攻击

## 开发流程
1. 拉取最新代码
2. 创建功能分支
3. 开发功能
4. 代码自测
5. 提交代码（符合 Git 提交规范）
6. 发起 Pull Request
7. Code Review
8. 合并代码

## 常用命令
```bash
# 安装依赖
pnpm install

# 开发
pnpm dev

# 构建
pnpm build

# 代码格式化
pnpm batch:prettier

# 清理缓存
pnpm clean:cache
```
