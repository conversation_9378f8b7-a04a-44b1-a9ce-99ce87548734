import { defHttp } from '/@/utils/http/axios';
import { ApiResult } from '/@/api/dataIntelligence/model/ragModel';

enum Api {
  // MCP服务注册相关接口
  listMcpServiceRegistry = '/mcp/server/list',
  addMcpServiceRegistry = '/mcp/server/add',
  editMcpServiceRegistry = '/mcp/server/edit',
  deleteMcpServiceRegistry = '/mcp/server/delete',
  deleteBatchMcpServiceRegistry = '/mcp/server/deleteBatch',
  queryMcpServiceByCode = '/mcp/server/queryByCode',
  queryMcpServiceById = '/mcp/server/queryById',
}

// MCP服务注册参数类型
export interface McpServiceRegistryItem {
  tid?: string;
  mcpName: string;
  mcpCode: string;
  mcpUrl: string;
  // mcpParam?: string;
  mcpServerParams?: Array<{
    fieldName: string;
    fieldType: string;
    fieldDesc: string;
  }>;
  mcpDesc?: string;
  createTime?: string;
  updateTime?: string;
  createUser?: string;
  updateUser?: string;
  delFlag?: number;
}

// MCP服务查询参数类型
export interface McpServiceRegistryQuery {
  mcpName?: string;
  mcpCode?: string;
  mcpDesc?: string;
  mcpUrl?: string;
  mcpParam?: string;
  createTime?: string;
  createUser?: string;
  updateTime?: string;
  updateUser?: string;
  tid?: string;
  pageNo?: number;
  pageSize?: number;
}

// MCP服务分页结果类型
export interface McpServicePageResult {
  records: McpServiceRegistryItem[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  optimizeCountSql: boolean;
  searchCount: boolean;
  maxLimit: number | null;
  countId: string | null;
  pages: number;
}

// MCP服务注册相关请求
export const getMcpServiceRegistryList = (params?: McpServiceRegistryQuery) =>
  defHttp.get<McpServicePageResult>({ url: Api.listMcpServiceRegistry, params });

export const addMcpServiceRegistry = (params: McpServiceRegistryItem) => defHttp.post<ApiResult<any>>({ url: Api.addMcpServiceRegistry, params });

export const editMcpServiceRegistry = (params: McpServiceRegistryItem) => defHttp.put<ApiResult<any>>({ url: Api.editMcpServiceRegistry, params });

export const deleteMcpServiceRegistry = (id: string) => defHttp.delete<ApiResult<any>>({ url: Api.deleteMcpServiceRegistry + '?id=' + id });

export const deleteBatchMcpServiceRegistry = (ids: string[]) =>
  defHttp.delete<ApiResult<any>>({ url: Api.deleteBatchMcpServiceRegistry + '?ids=' + ids.join(',') });

// MCP服务通过编码查询
export const queryMcpServiceByCode = (mcpCode: string) =>
  defHttp.get<McpServiceRegistryItem>({ url: Api.queryMcpServiceByCode + '?mcpCode=' + mcpCode });

// MCP服务通过id查询
export const queryMcpServiceById = (id: string) => defHttp.get<ApiResult<McpServiceRegistryItem>>({ url: Api.queryMcpServiceById + '?id=' + id });
