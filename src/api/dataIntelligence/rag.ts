import { defHttp } from '/@/utils/http/axios';
import type {
  KnowledgeBaseParams,
  AddKnowledgeBaseParams,
  EditKnowledgeBaseParams,
  KnowledgeBasePageResult,
  KnowledgeBaseDetailResult,
  KnowledgeFileParams,
  KnowledgeFilePageResult,
  KnowledgeFileUploadParams,
  KnowledgeFileUploadResult,
  KnowledgeFileChunksParams,
  KnowledgeFileChunksResult,
  KnowledgeFileDeleteParams,
  KnowledgeFileDeleteResult,
  ApiResult,
} from './model/ragModel';

enum Api {
  // 知识库相关接口
  KnowledgeBaseList = '/al/alKnowledgeBase/list',
  KnowledgeBaseAdd = '/al/alKnowledgeBase/add',
  KnowledgeBaseDelete = '/al/alKnowledgeBase/delete',
  KnowledgeBaseDeleteBatch = '/al/alKnowledgeBase/deleteBatch',
  KnowledgeBaseEdit = '/al/alKnowledgeBase/edit',
  KnowledgeBaseQueryById = '/al/alKnowledgeBase/queryById',
  KnowledgeFileList = '/al/alKnowledgeFile/list',
  // KnowledgeFileUpload = 'http://10.154.165.164:18000/api/rag/upload',
  // KnowledgeFileChunks = 'http://10.154.165.164:18000/api/rag/files/chunks',
  // KnowledgeFileDelete = 'http://10.154.165.164:18000/api/rag/delete/file',
  KnowledgeFileUpload = '/api/rag/shangchuan',
  KnowledgeFileChunks = '/api/rag/files/chunks',
  KnowledgeFileDelete = '/api/rag/delete/file',
}

/**
 * 知识库-分页列表查询
 * @param params 查询参数
 * @returns 分页查询结果
 */
export const getKnowledgeBaseList = (params?: KnowledgeBaseParams): Promise<KnowledgeBasePageResult> => {
  return defHttp.get({
    url: Api.KnowledgeBaseList,
    params,
  });
};

/**
 * 新增知识库
 * @param params 新增参数
 * @returns 新增结果
 */
export const addKnowledgeBase = (params: AddKnowledgeBaseParams): Promise<ApiResult> => {
  return defHttp.post({
    url: Api.KnowledgeBaseAdd,
    params,
  });
};

/**
 * 通过id删除知识库
 * @param id 知识库ID
 * @returns 删除结果
 */
export const deleteKnowledgeBase = (id: string): Promise<ApiResult> => {
  return defHttp.delete({
    url: Api.KnowledgeBaseDelete + '?id=' + id,
  });
};

/**
 * 批量删除知识库
 * @param ids 知识库ID数组，以逗号分隔的字符串
 * @returns 删除结果
 */
export const deleteBatchKnowledgeBase = (ids: string): Promise<ApiResult> => {
  return defHttp.delete({
    url: Api.KnowledgeBaseDeleteBatch + '?ids=' + ids,
  });
};

/**
 * 知识库编辑
 * @param params 编辑参数
 * @returns 编辑结果
 */
export const editKnowledgeBase = (params: EditKnowledgeBaseParams): Promise<ApiResult> => {
  return defHttp.post({
    url: Api.KnowledgeBaseEdit,
    params,
  });
};

/**
 * 通过id查询知识库详情
 * @param id 知识库ID
 * @returns 知识库详情
 */
export const getKnowledgeBaseById = (id: string): Promise<KnowledgeBaseDetailResult> => {
  return defHttp.get({
    url: Api.KnowledgeBaseQueryById,
    params: { id },
  });
};

/**
 * 知识文件列表分页查询
 * @param params 查询参数
 * @returns 知识文件列表分页结果
 */
export const getKnowledgeFileList = (params?: KnowledgeFileParams): Promise<KnowledgeFilePageResult> => {
  return defHttp.get({
    url: Api.KnowledgeFileList,
    params,
  }) as Promise<KnowledgeFilePageResult>;
};

/**
 * 上传文件到知识库并向量化
 * @param params 上传参数
 * @returns 上传结果
 */
export const uploadKnowledgeFile = (params: KnowledgeFileUploadParams): Promise<KnowledgeFileUploadResult> => {
  console.log('上传文件:', params.files, '知识库ID:', params.knowledge_base_id);

  // 使用系统提供的uploadFile方法
  return defHttp.uploadFile<KnowledgeFileUploadResult>(
    {
      url: Api.KnowledgeFileUpload,
    },
    {
      name: 'files',
      file: params.files as File,
      data: {
        knowledge_base_id: params.knowledge_base_id,
      },
    },
    {
      isReturnResponse: true,
    }
  );
};

/**
 * 获取文件的所有chunks
 * @param params 查询参数
 * @returns chunks列表
 */
export const getKnowledgeFileChunks = (params: KnowledgeFileChunksParams): Promise<KnowledgeFileChunksResult> => {
  const { file_id, knowledge_base_id } = params;
  return defHttp.get<KnowledgeFileChunksResult>({
    url: `${Api.KnowledgeFileChunks}?file_id=${file_id}&knowledge_base_id=${knowledge_base_id}`,
  });
};

/**
 * 删除知识库文件
 * @param params 删除参数
 * @returns 删除结果
 */
export const deleteKnowledgeFile = (params: KnowledgeFileDeleteParams): Promise<KnowledgeFileDeleteResult> => {
  const { file_id, knowledge_base_id } = params;
  return defHttp.delete<KnowledgeFileDeleteResult>({
    url: `${Api.KnowledgeFileDelete}?file_id=${file_id}&knowledge_base_id=${knowledge_base_id}`,
  });
};

// 导出所有API方法
export default {
  getKnowledgeBaseList,
  addKnowledgeBase,
  deleteKnowledgeBase,
  deleteBatchKnowledgeBase,
  editKnowledgeBase,
  getKnowledgeBaseById,
  getKnowledgeFileList,
  uploadKnowledgeFile,
  getKnowledgeFileChunks,
  deleteKnowledgeFile,
};
