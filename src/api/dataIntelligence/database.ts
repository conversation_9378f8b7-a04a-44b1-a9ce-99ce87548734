import { defHttp } from '/@/utils/http/axios';
import { BasicFetchResult } from '/@/api/model/baseModel';

enum Api {
  Add = '/database/add',
  Delete = '/database/delete',
  Edit = '/database/edit',
  List = '/database/list',
  QueryById = '/database/queryById',
  DeleteBatch = '/database/deleteBatch',
  TestConnection = '/database/testConnection',
  QueryByAgentId = '/database/queryByAgentId',
  AddAgent = '/database/addAgent',
  EditAgent = '/database/editAgent',
  TableList = '/database/table/list',
  TablePageQueryNoAgentTable = '/database/table/pageQueryNoAgentTable',
  TableAddBatch = '/database/table/addBatch',
  TableDeleteBatch = '/database/table/deleteBatch',
}

/**
 * 数据表信息
 */
export interface TableItem {
  agentId?: string;
  createTime?: string;
  createUser?: string;
  databaseId?: string;
  tableDesc?: string;
  tableName?: string;
  tid?: string;
  updateTime?: string;
  updateUser?: string;
}

/**
 * 数据库信息
 */
export interface DatabaseItem {
  agentId?: string;
  createTime?: string;
  createUser?: string;
  database?: string;
  host?: string;
  isTableExists?: number;
  password?: string;
  port?: number;
  tableList?: TableItem[];
  tid?: string;
  type?: string;
  updateTime?: string;
  updateUser?: string;
  url?: string;
  username?: string;
  connectUrl?: string;
}

/**
 * 数据库Agent信息
 */
export interface DatabaseAgentItem {
  tid?: string;
  agentName?: string;
  agentCode?: string;
  agentDesc?: string;
  databaseId?: string;
  type?: string;
  url?: string;
  username?: string;
  password?: string;
  createTime?: string;
  updateTime?: string;
  createUser?: string;
  updateUser?: string;
}

/**
 * 数据库查询参数
 */
export interface DatabaseParams {
  agentId?: string;
  createTime?: string;
  createUser?: string;
  database?: string;
  host?: string;
  isTableExists?: number;
  password?: string;
  port?: number;
  tid?: string;
  type?: string;
  updateTime?: string;
  updateUser?: string;
  url?: string;
  username?: string;
  connectUrl?: string;
  pageNo?: number;
  pageSize?: number;
}

/**
 * 添加数据库
 */
export function addDatabase(params: DatabaseItem) {
  return defHttp.post<DatabaseItem>({
    url: Api.Add,
    params,
  });
}

/**
 * 删除数据库
 */
export function deleteDatabase(id: string) {
  return defHttp.delete<DatabaseItem>({
    url: Api.Delete + '?id=' + id,
  });
}

/**
 * 批量删除数据库
 */
export function deleteBatchDatabase(ids: string[]) {
  return defHttp.delete({
    url: Api.DeleteBatch + '?ids=' + ids.join(','),
  });
}

/**
 * 编辑数据库
 */
export function editDatabase(params: DatabaseItem) {
  return defHttp.put<DatabaseItem>({
    url: Api.Edit,
    params,
  });
}

/**
 * 获取数据库列表
 */
export function getDatabaseList(params?: DatabaseParams) {
  return defHttp.get<BasicFetchResult<DatabaseItem>>({
    url: Api.List,
    params,
  });
}

/**
 * 根据ID获取数据库信息
 */
export function getDatabaseById(id: string) {
  return defHttp.get<DatabaseItem>({
    url: Api.QueryById,
    params: { id },
  });
}

/**
 * 测试数据库连接
 */
export function testDatabaseConnection(params: DatabaseItem) {
  return defHttp.post({
    url: Api.TestConnection,
    params,
  });
}

/**
 * 编辑数据库agent
 */
export function editDatabaseAgent(params: DatabaseAgentItem) {
  return defHttp.post<DatabaseAgentItem>({
    url: Api.EditAgent,
    params,
  });
}

/**
 * 根据agentId获取数据库Agent信息
 */
export function getDatabaseByAgentId(agentId: string) {
  return defHttp.get<DatabaseAgentItem>({
    url: Api.QueryByAgentId,
    params: { agentId },
  });
}

/**
 * 添加数据库Agent
 */
export function addDatabaseAgent(params: DatabaseAgentItem) {
  return defHttp.post<DatabaseAgentItem>({
    url: Api.AddAgent,
    params,
  });
}

/**
 * 根据数据库id查询绑定的数据库表
 */
export function getDatabaseTableList(params) {
  return defHttp.get({
    url: Api.TableList,
    params,
  });
}

/**
 * 数据库管理相关-分页查询未绑定的数据表
 */
export function QueryNoAgentTable(params) {
  return defHttp.get({
    url: Api.TablePageQueryNoAgentTable,
    params,
  });
}

/**
 * 数据库管理相关-批量添加数据表
 */
export function addBatchTable(params) {
  return defHttp.post({
    url: Api.TableAddBatch,
    params,
  });
}

/**
 * 数据库管理相关-批量删除数据表
 */
export function deleteBatchTable(params) {
  return defHttp.delete({
    url: Api.TableDeleteBatch + '?ids=' + params.ids,
  });
}

// 数据库类型枚举
export enum DatabaseType {
  MYSQL = 'MySQL',
  ORACLE = 'Oracle',
  POSTGRESQL = 'PostgreSQL',
  DM = 'DM',
}

// 是否存在数据表枚举
export enum TableExistsEnum {
  NO = 0,
  YES = 1,
}
