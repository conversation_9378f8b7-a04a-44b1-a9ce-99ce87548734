import { defHttp } from '/@/utils/http/axios';
import { BasicFetchResult } from '/@/api/model/baseModel';

enum Api {
  list = '/project/list',
  add = '/project/add',
  edit = '/project/edit',
  delete = '/project/delete',
  deleteBatch = '/project/deleteBatch',
  queryByCode = '/project/queryByCode',
  relatedAgent = '/project/relatedAgent',
  cancelRelatedAgent = '/project/cancelRelatedToAgent',
}

/**
 * 项目实体
 */
export interface ProjectItem {
  id?: string;
  projectCode: string;
  projectName: string;
  projectLeader: string;
  projectAddress: string;
  createTime?: string;
  createUser?: string;
  updateTime?: string;
  updateUser?: string;
  tid?: string;
}

/**
 * 分页参数
 */
export interface ProjectPageParams {
  pageNo: number;
  pageSize: number;
  projectCode?: string;
  projectName?: string;
  projectLeader?: string;
  projectAddress?: string;
  createTime?: string;
  createUser?: string;
  updateTime?: string;
  updateUser?: string;
  tid?: string;
}

/**
 * 获取项目列表
 */
export const getProjectList = (params: ProjectPageParams) => {
  return defHttp.get<BasicFetchResult<ProjectItem>>({
    url: Api.list,
    params,
  });
};

/**
 * 添加项目
 */
export const addProject = (params: ProjectItem) => {
  return defHttp.post<ProjectItem>({
    url: Api.add,
    params,
  });
};

/**
 * 编辑项目
 */
export const editProject = (params: ProjectItem) => {
  return defHttp.put<ProjectItem>({
    url: Api.edit,
    params,
  });
};

/**
 * 删除项目
 */
export const deleteProject = (id: string) => {
  return defHttp.delete({
    url: Api.delete + '?id=' + id,
  });
};

/**
 * 批量删除项目
 */
export const deleteBatchProject = (ids: string) => {
  return defHttp.delete({
    url: Api.deleteBatch + '?ids=' + ids,
  });
};

/**
 * 根据编码查询项目
 */
export const queryProjectByCode = (projectCode: string) => {
  return defHttp.get<ProjectItem>({
    url: Api.queryByCode,
    params: { projectCode },
  });
};

/**
 * 关联Agent
 */
export const relateAgent = (params: { projectId: string; agentIds: string }) => {
  return defHttp.post({
    url: Api.relatedAgent,
    params,
  });
};

/**
 * 解除关联Agent
 */
export const cancelRelatedToAgent = (params: { projectId: string; agentIds: string }) => {
  return defHttp.post({
    url: Api.cancelRelatedAgent,
    params,
  });
};
