import { defHttp } from '/@/utils/http/axios';
import { BasicFetchResult } from '/@/api/model/baseModel';

enum Api {
  list = '/agent/list',
  add = '/agent/add',
  edit = '/agent/edit',
  delete = '/agent/delete',
  deleteBatch = '/agent/deleteBatch',
  queryByCode = '/agent/queryByCode',
  relatedToAgent = '/agent/relatedToAgent',
  cancelRelatedToAgent = '/agent/cancelRelatedToAgent',
}

/**
 * Agent类型
 */
export enum AgentType {
  INTERFACE = 1,
  TOOL = 2,
  DATABASE = 3,
  RAG = 4,
}

/**
 * Agent实体
 */
export interface AgentItem {
  agentId?: string;
  id?: string;
  agentCode: string;
  agentName: string;
  agentType: string | number;
  description?: string;
  agentDesc?: string;
  createTime?: string;
  createUser?: string;
  updateTime?: string;
  updateUser?: string;
  tid?: string;
}

/**
 * 分页参数
 */
export interface AgentPageParams {
  pageNo: number;
  pageSize: number;
  agentCode?: string;
  agentName?: string;
  agentType?: number;
  description?: string;
  createTime?: string;
  createUser?: string;
  updateTime?: string;
  updateUser?: string;
  tid?: string;
}

/**
 * 获取Agent列表
 */
export const getAgentList = (params: AgentPageParams) => {
  return defHttp.get<BasicFetchResult<AgentItem>>({
    url: Api.list,
    params,
  });
};

/**
 * 添加Agent
 */
export const addAgent = (params: AgentItem) => {
  return defHttp.post<AgentItem>({
    url: Api.add,
    params,
  });
};

/**
 * 编辑Agent
 */
export const editAgent = (params: AgentItem) => {
  return defHttp.put<AgentItem>({
    url: Api.edit,
    params,
  });
};

/**
 * 删除Agent
 */
export const deleteAgent = (tid: string) => {
  return defHttp.delete({
    url: Api.delete + '?id=' + tid,
  });
};

/**
 * 批量删除Agent
 */
export const deleteBatchAgent = (ids: string) => {
  return defHttp.delete({
    url: Api.deleteBatch,
    params: { ids },
  });
};

/**
 * 根据编码查询Agent
 */
export const queryAgentByCode = (agentCode: string) => {
  return defHttp.get<AgentItem>({
    url: Api.queryByCode,
    params: { agentCode },
  });
};

/**
 * 关联Agent
 */
export const relateToAgent = (params: { agentId: string; relatedIds: string; relatedType: string }) => {
  return defHttp.post({
    url: Api.relatedToAgent,
    params,
  });
};

/**
 * 解除关联Agent
 */
export const cancelRelateToAgent = (params: { agentId: string; relatedIds: string; relatedType: number }) => {
  return defHttp.post({
    url: Api.cancelRelatedToAgent,
    params,
  });
};
