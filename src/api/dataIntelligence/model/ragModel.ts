/**
 * 知识库查询参数接口
 */
export interface KnowledgeBaseParams {
  chunkOverlap?: number; // 文本切块重叠长度
  chunkSize?: number; // 文本切块最大长度
  code?: string; // 知识库编码
  dbType?: string; // 向量库类型
  dbUrl?: string; // 向量库地址
  describle?: string; // 知识库描述
  embeddingModel?: string; // embedding模型，关联模型配置表
  id?: string; // 主键
  name?: string; // 知识库名称
  rerankModel?: string; // rerank模型，关联模型配置表
  scoreThreshold?: number; // 检索相似度设置
  separators?: string; // 分段标识符
  topK?: number; // 检索文本块
  updateTime?: string; // 更新时间
  pageNo?: number; // 页码
  pageSize?: number; // 页面大小
}

/**
 * 知识库实体接口
 */
export interface KnowledgeBaseModel {
  id?: string; // 主键
  code?: string; // 知识库编码
  name: string; // 知识库名称
  describle?: string; // 知识库描述
  dbType?: string; // 向量库类型
  dbUrl?: string; // 向量库地址
  embeddingModel?: string; // embedding模型
  rerankModel?: string; // rerank模型
  chunkSize?: number; // 文本切块最大长度
  chunkOverlap?: number; // 文本切块重叠长度
  separators?: string; // 分段标识符
  topK?: number; // 检索文本块
  scoreThreshold?: number; // 检索相似度设置
  updateTime?: string; // 更新时间
}

/**
 * 新增知识库参数接口
 */
export interface AddKnowledgeBaseParams {
  chunkOverlap?: number;
  chunkSize?: number;
  dbType?: string;
  dbUrl?: string;
  describle?: string;
  embeddingModel?: string;
  name: string;
  rerankModel?: string;
  scoreThreshold?: number;
  separators?: string;
  topK?: number;
}

/**
 * 编辑知识库参数接口
 */
export interface EditKnowledgeBaseParams extends AddKnowledgeBaseParams {
  id: string;
  code?: string;
  updateTime?: string;
}

/**
 * 分页查询结果接口
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * API响应接口
 */
export interface ApiResult<T = any> {
  success: boolean;
  result: T;
  message: string;
  code: number;
}

/**
 * 知识库分页查询结果
 */
export type KnowledgeBasePageResult = ApiResult<PageResult<KnowledgeBaseModel>>;

/**
 * 知识库详情查询结果
 */
export type KnowledgeBaseDetailResult = ApiResult<KnowledgeBaseModel>;

/**
 * 知识库文件查询参数接口
 */
export interface KnowledgeFileParams {
  createdTime?: string; // 创建时间
  fileAddr?: string; // 文件地址
  fileCode?: string; // 文件编码
  fileName?: string; // 文件名称
  fileStore?: string; // 存储类型;本地，OSS
  fileType?: string; // 文件类型;（folder:文件夹 excel:excel doc:word ppt:ppt image:图片 archive:其他文档 video:视频 pdf:pdf）
  fileVisitUrl?: string; // 文件访问地址
  id?: number; // 主键
  isDelete?: string; // 是否删除（0否，1是）
  knowledgeId?: string; // 知识库ID
  updatedTime?: string; // 更新时间
  pageNo?: number; // 页码
  pageSize?: number; // 页面大小
}

/**
 * 知识库文件实体接口
 */
export interface KnowledgeFileModel {
  id?: number; // 主键
  fileCode?: string; // 文件编码
  fileName?: string; // 文件名称
  fileAddr?: string; // 文件地址
  fileVisitUrl?: string; // 文件访问地址
  fileStore?: string; // 存储类型
  fileType?: string; // 文件类型
  knowledgeId?: string; // 知识库ID
  isDelete?: string; // 是否删除
  createdTime?: string; // 创建时间
  updatedTime?: string; // 更新时间
  fileSize?: string; // 文件大小（前端显示用）
}

/**
 * 知识库文件分页查询结果
 */
export interface KnowledgeFilePageResult {
  records: KnowledgeFileModel[];
  total: number;
  size: number;
  current: number;
  orders: any[];
  optimizeCountSql: boolean;
  searchCount: boolean;
  countId: string | null;
  maxLimit: number | null;
  pages: number;
}

/**
 * 文件上传参数
 */
export interface KnowledgeFileUploadParams {
  files: File;
  knowledge_base_id: string;
}

/**
 * 获取文件chunks参数
 */
export interface KnowledgeFileChunksParams {
  file_id: string;
  knowledge_base_id: string;
}

/**
 * 删除文件参数
 */
export interface KnowledgeFileDeleteParams {
  file_id: string;
  knowledge_base_id: string;
}

/**
 * 文件上传响应结果
 */
export interface KnowledgeFileUploadResult {
  code: number;
  message: string;
  data: {
    file_id: string;
    file_code: string;
    file_name: string;
    knowledge_base_id: string;
  };
}

/**
 * 文件chunk信息
 */
export interface KnowledgeFileChunk {
  id: string; // 文件切块唯一标识
  text: string; // 文件切块内容
  source: string; // 文件来源
  page: number; // 切块排序
  chunk_id: string; // 文件切块唯一标识
  metadata: string; // 元数据JSON字符串
  text_length: number; // 文本长度
}

/**
 * 获取文件chunks响应结果
 */
export interface KnowledgeFileChunksResult {
  chunks: KnowledgeFileChunk[];
  file_info: {
    file_id: string; // 文件唯一标识
    file_name: string; // 文件名称
    knowledge_base_id: string; // 知识库唯一标识
  };
  total_chunks: number; // 文件分块块数
  total_characters: number; // 总字符数
}

/**
 * 删除文件响应结果
 */
export interface KnowledgeFileDeleteResult {
  code: number;
  message: string;
  data: boolean;
}
