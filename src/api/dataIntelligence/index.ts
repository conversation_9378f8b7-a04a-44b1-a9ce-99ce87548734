import { defHttp } from '/@/utils/http/axios';
import { ApiResult } from '/@/api/dataIntelligence/model/ragModel';

enum Api {
  // API注册相关接口
  listApiRegistry = '/interfaces/list',
  addApiRegistry = '/interfaces/add',
  editApiRegistry = '/interfaces/edit',
  deleteApiRegistry = '/interfaces/delete',
  deleteBatchApiRegistry = '/interfaces/deleteBatch',
  queryApiRegistryByCode = '/interfaces/queryByCode',
}

// API注册接口参数类型
export interface ApiRegistryItem {
  tid?: string;
  interfaceName: string;
  interfaceCode: string;
  interfaceDesc?: string;
  interfaceType: 'GET' | 'POST' | 'PUT' | 'DELETE';
  interfaceUrl: string;
  interfaceParams?: string;
  interfaceOutputParams?: string;
  enable: 0 | 1;
  mcpServerId?: string; // 关联的MCP服务ID
  mcpServerName?: string; // 关联的MCP服务名称
  requestParams?: Array<{
    fieldName: string;
    fieldType: string;
    fieldDesc: string;
  }>;
  responseParams?: Array<{
    fieldName: string;
    fieldType: string;
    fieldDesc: string;
  }>;
  headerParams?: Array<{
    fieldName: string;
    fieldType: string;
    fieldDesc: string;
  }>;
  headers?: Array<{
    fieldName: string;
    fieldType: string;
    fieldValue: string;
  }>;
  createTime?: string;
  updateTime?: string;
  createUser?: string;
  updateUser?: string;
}

// 查询参数类型
export interface ApiRegistryQuery {
  interfaceCode?: string;
  interfaceName?: string;
  interfaceType?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  enable?: 0 | 1;
  pageNo?: number;
  pageSize?: number;
}

// API注册相关请求
export const getApiRegistryList = (params?: ApiRegistryQuery) => defHttp.get<ApiResult<ApiRegistryItem[]>>({ url: Api.listApiRegistry, params });

export const addApiRegistry = (params: ApiRegistryItem) => defHttp.post<ApiResult<any>>({ url: Api.addApiRegistry, params });

export const editApiRegistry = (params: ApiRegistryItem) => defHttp.put<ApiResult<any>>({ url: Api.editApiRegistry, params });

export const deleteApiRegistry = (id: string) => defHttp.delete<ApiResult<any>>({ url: Api.deleteApiRegistry + '?id=' + id });

export const deleteBatchApiRegistry = (ids: string[]) =>
  defHttp.delete<ApiResult<any>>({ url: Api.deleteBatchApiRegistry + '?ids=' + ids.join(',') });

export const queryApiRegistryByCode = (interfaceCode: string) =>
  defHttp.get<ApiRegistryItem>({ url: Api.queryApiRegistryByCode + '?interfaceCode=' + interfaceCode });

// 导出MCP服务相关API
export * from './mcp';

// 导出RAG相关API
export * from './rag';

// 导出模型类型
export * from './model/ragModel';
